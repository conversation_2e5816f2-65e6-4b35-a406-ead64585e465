"""加密工具模块

提供密码加密、解密和密钥生成功能。
"""

import os
import base64
import secrets
from cryptography.fernet import Fernet
from core.logging import get_logger

logger = get_logger(__name__)


class CryptoUtils:
    """加密工具类"""
    
    @staticmethod
    def generate_encryption_key() -> str:
        """生成32字节的加密密钥
        
        Returns:
            base64编码的32字节密钥字符串
        """
        # 生成32字节的随机密钥
        key = secrets.token_bytes(32)
        # 返回base64编码的字符串
        return base64.urlsafe_b64encode(key).decode()
    
    @staticmethod
    def generate_fernet_key() -> str:
        """生成Fernet兼容的加密密钥
        
        Returns:
            Fernet密钥字符串
        """
        return Fernet.generate_key().decode()
    
    @staticmethod
    def create_cipher(key: str) -> Fernet:
        """创建Fernet加密器
        
        Args:
            key: 加密密钥
            
        Returns:
            Fernet加密器实例
        """
        try:
            # 如果密钥长度是32字节，转换为Fernet密钥
            if len(key.encode()) == 32:
                fernet_key = base64.urlsafe_b64encode(key.encode())
            else:
                # 假设已经是Fernet密钥
                fernet_key = key.encode()
            
            return Fernet(fernet_key)
        except Exception as e:
            logger.error(f"创建加密器失败: {e}")
            raise
    
    @staticmethod
    def encrypt_password(password: str, key: str) -> str:
        """加密密码
        
        Args:
            password: 明文密码
            key: 加密密钥
            
        Returns:
            加密后的密码（base64编码）
        """
        try:
            cipher = CryptoUtils.create_cipher(key)
            encrypted = cipher.encrypt(password.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"密码加密失败: {e}")
            raise
    
    @staticmethod
    def decrypt_password(encrypted_password: str, key: str) -> str:
        """解密密码
        
        Args:
            encrypted_password: 加密的密码（base64编码）
            key: 加密密钥
            
        Returns:
            明文密码
        """
        try:
            cipher = CryptoUtils.create_cipher(key)
            encrypted_data = base64.urlsafe_b64decode(encrypted_password.encode())
            decrypted = cipher.decrypt(encrypted_data)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"密码解密失败: {e}")
            raise
    
    @staticmethod
    def validate_key(key: str) -> bool:
        """验证加密密钥是否有效
        
        Args:
            key: 加密密钥
            
        Returns:
            密钥是否有效
        """
        try:
            CryptoUtils.create_cipher(key)
            return True
        except Exception:
            return False
    
    @staticmethod
    def generate_secure_config_template() -> str:
        """生成安全配置模板
        
        Returns:
            包含新生成密钥的配置模板
        """
        key = CryptoUtils.generate_fernet_key()
        
        template = f"""
# 凭据管理安全配置
# 请将以下配置添加到您的 .env 文件中

# 凭据加密密钥（请妥善保管，丢失后无法解密已存储的密码）
CREDENTIAL_ENCRYPTION_KEY={key}

# 凭据管理其他配置
CREDENTIAL_REDIS_PREFIX=server_credential
CREDENTIAL_EXPIRE_TIME=31536000  # 1年
ENABLE_CREDENTIAL_AUDIT=true
CREDENTIAL_BACKUP_ENABLED=true

# 安全建议：
# 1. 请将加密密钥存储在安全的地方
# 2. 不要将密钥提交到版本控制系统
# 3. 定期备份凭据数据
# 4. 启用审计日志以跟踪凭据操作
"""
        return template.strip()


def generate_key_command():
    """命令行工具：生成新的加密密钥"""
    print("正在生成新的凭据加密密钥...")
    print()
    print(CryptoUtils.generate_secure_config_template())
    print()
    print("请将上述配置添加到您的 .env 文件中。")
    print("警告：请妥善保管加密密钥，丢失后无法解密已存储的密码！")


if __name__ == "__main__":
    generate_key_command()
