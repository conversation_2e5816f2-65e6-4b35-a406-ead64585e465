# GWHW网关MCP服务配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# ==================== 服务配置 ====================
# 服务监听地址
HOST=0.0.0.0
# 服务端口
PORT=8000
# 调试模式
DEBUG=false

# ==================== Redis配置 ====================
# Redis服务器地址
REDIS_HOST=**************
# Redis端口
REDIS_PORT=6379
# Redis数据库编号
REDIS_DB=0
# Redis密码
REDIS_PASSWORD=qzkj

# ==================== 默认SSH配置 ====================
# 默认远程服务器IP
DEFAULT_REMOTE_IP=*************
# 默认SSH用户名
DEFAULT_SSH_USER=root
# 默认容器名称
DEFAULT_CONTAINER_NAME=build

# ==================== 默认路径配置 ====================
# 默认构建脚本路径
DEFAULT_BUILD_SCRIPT_PATH=/home/<USER>/build
# 默认容器内插件路径
DEFAULT_CONTAINER_PLUGIN_PATH=/home/<USER>/src/hw/gw_parser/parser/
# 默认服务器插件路径
DEFAULT_SERVER_PLUGIN_PATH=/opt/apigw/gwhw/parser/
# 默认并行编译任务数
DEFAULT_MAKE_JOBS=4

# ==================== 测试配置 ====================
# 默认等待处理时间（秒）
DEFAULT_WAIT_SECONDS=5
# 默认日志输出行数
DEFAULT_LOG_LINES=100
# pcap任务目录
PCAP_TASK_DIR=/opt/pcap/task
# pcap测试目录（用于回放测试）
PCAP_TEST_DIR=/opt/pcap/
# 上传日志文件路径
UPLOAD_LOG_FILE=/opt/upload/log.file
# 网关日志文件路径
HW_LOG_PATH=/opt/apigw/gwhw/logs/hw.log
# 网关错误日志路径
HW_ERR_PATH=/opt/apigw/gwhw/logs/hw.err

# ==================== 日志配置 ====================
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
# 日志文件路径
LOG_FILE=logs/gwhw_mcp.log
# 错误日志文件路径
LOG_ERR=logs/gwhw_mcp.err