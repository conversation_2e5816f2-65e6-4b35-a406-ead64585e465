#!/usr/bin/env python3
"""凭据数据迁移脚本

将凭据数据从Redis 0号数据库迁移到1号数据库。
"""

import asyncio
import redis.asyncio as redis
from config.settings import settings
from core.logging import get_logger, setup_logging

# 设置日志
setup_logging()
logger = get_logger(__name__)


async def migrate_credentials():
    """迁移凭据数据"""
    
    # 连接到源数据库（0号）
    source_client = redis.Redis(
        host=settings.redis_host,
        port=settings.redis_port,
        password=settings.redis_password,
        db=0,  # 源数据库
        decode_responses=True
    )
    
    # 连接到目标数据库（1号）
    target_client = redis.Redis(
        host=settings.redis_host,
        port=settings.redis_port,
        password=settings.redis_password,
        db=1,  # 目标数据库
        decode_responses=True
    )
    
    try:
        # 测试连接
        await source_client.ping()
        await target_client.ping()
        logger.info("Redis连接测试成功")
        
        # 查找所有凭据键
        credential_pattern = f"{settings.credential_redis_prefix}:*"
        logger.info(f"搜索凭据键模式: {credential_pattern}")
        
        # 扫描所有匹配的键
        keys = []
        cursor = 0
        while True:
            cursor, batch_keys = await source_client.scan(cursor, match=credential_pattern, count=100)
            keys.extend(batch_keys)
            if cursor == 0:
                break
        
        logger.info(f"找到 {len(keys)} 个凭据键")
        
        if not keys:
            logger.info("没有找到需要迁移的凭据数据")
            return
        
        # 迁移数据
        migrated_count = 0
        failed_count = 0
        
        for key in keys:
            try:
                # 从源数据库获取数据
                data = await source_client.get(key)
                if data:
                    # 获取TTL
                    ttl = await source_client.ttl(key)
                    
                    # 写入目标数据库
                    await target_client.set(key, data)
                    
                    # 设置TTL（如果有的话）
                    if ttl > 0:
                        await target_client.expire(key, ttl)
                    
                    migrated_count += 1
                    logger.info(f"迁移成功: {key}")
                else:
                    logger.warning(f"键 {key} 没有数据")
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"迁移失败 {key}: {e}")
        
        logger.info(f"迁移完成！成功: {migrated_count}, 失败: {failed_count}")
        
        # 验证迁移结果
        logger.info("验证迁移结果...")
        for key in keys[:5]:  # 验证前5个键
            try:
                source_data = await source_client.get(key)
                target_data = await target_client.get(key)
                
                if source_data == target_data:
                    logger.info(f"验证成功: {key}")
                else:
                    logger.error(f"验证失败: {key} - 数据不匹配")
                    
            except Exception as e:
                logger.error(f"验证失败 {key}: {e}")
        
        # 询问是否删除源数据库中的凭据数据
        print("\n" + "="*60)
        print("迁移完成！")
        print(f"成功迁移 {migrated_count} 个凭据")
        print(f"失败 {failed_count} 个凭据")
        print("\n是否删除源数据库（DB0）中的凭据数据？")
        print("注意：删除后无法恢复！")
        
        while True:
            choice = input("请输入 (y/n): ").lower().strip()
            if choice in ['y', 'yes']:
                # 删除源数据库中的凭据数据
                deleted_count = 0
                for key in keys:
                    try:
                        result = await source_client.delete(key)
                        if result:
                            deleted_count += 1
                            logger.info(f"删除成功: {key}")
                    except Exception as e:
                        logger.error(f"删除失败 {key}: {e}")
                
                logger.info(f"删除完成！删除了 {deleted_count} 个键")
                break
                
            elif choice in ['n', 'no']:
                logger.info("保留源数据库中的凭据数据")
                break
            else:
                print("请输入 y 或 n")
        
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")
        
    finally:
        # 关闭连接
        await source_client.close()
        await target_client.close()
        logger.info("Redis连接已关闭")


async def check_migration_status():
    """检查迁移状态"""
    
    # 连接到两个数据库
    db0_client = redis.Redis(
        host=settings.redis_host,
        port=settings.redis_port,
        password=settings.redis_password,
        db=0,
        decode_responses=True
    )
    
    db1_client = redis.Redis(
        host=settings.redis_host,
        port=settings.redis_port,
        password=settings.redis_password,
        db=1,
        decode_responses=True
    )
    
    try:
        credential_pattern = f"{settings.credential_redis_prefix}:*"
        
        # 检查DB0中的凭据数量
        keys_db0 = []
        cursor = 0
        while True:
            cursor, batch_keys = await db0_client.scan(cursor, match=credential_pattern, count=100)
            keys_db0.extend(batch_keys)
            if cursor == 0:
                break
        
        # 检查DB1中的凭据数量
        keys_db1 = []
        cursor = 0
        while True:
            cursor, batch_keys = await db1_client.scan(cursor, match=credential_pattern, count=100)
            keys_db1.extend(batch_keys)
            if cursor == 0:
                break
        
        print("\n" + "="*60)
        print("凭据数据分布状态:")
        print(f"DB0 (任务数据库): {len(keys_db0)} 个凭据")
        print(f"DB1 (凭据数据库): {len(keys_db1)} 个凭据")
        print("="*60)
        
        if len(keys_db0) > 0 and len(keys_db1) > 0:
            print("⚠️  警告：两个数据库都有凭据数据，建议完成迁移")
        elif len(keys_db0) > 0 and len(keys_db1) == 0:
            print("📋 状态：凭据数据在DB0中，需要迁移到DB1")
        elif len(keys_db0) == 0 and len(keys_db1) > 0:
            print("✅ 状态：凭据数据已在DB1中，迁移完成")
        else:
            print("❓ 状态：没有找到凭据数据")
        
    except Exception as e:
        logger.error(f"检查状态失败: {e}")
        
    finally:
        await db0_client.close()
        await db1_client.close()


def main():
    """主函数"""
    print("GWHW网关MCP服务 - 凭据数据迁移工具")
    print("="*60)
    print("1. 检查迁移状态")
    print("2. 执行迁移")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == "1":
            asyncio.run(check_migration_status())
        elif choice == "2":
            asyncio.run(migrate_credentials())
        elif choice == "3":
            print("退出迁移工具")
            break
        else:
            print("无效选择，请输入 1-3")


if __name__ == "__main__":
    main()
