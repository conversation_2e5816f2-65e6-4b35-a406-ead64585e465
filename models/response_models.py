"""响应相关数据模型

定义API响应的数据结构。
"""

from datetime import datetime
from typing import Optional, Any
from pydantic import BaseModel, Field
from .task_models import TaskStatus


class TaskResponse(BaseModel):
    """任务创建响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    message: str = Field(..., description="任务消息")
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    result: Optional[dict[str, Any]] = Field(None, description="执行结果")
    
    class Config:
        use_enum_values = True


class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    message: str = Field(..., description="任务消息")
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    class Config:
        use_enum_values = True


class BuildStatusResponse(TaskStatusResponse):
    """构建任务状态响应模型"""
    stdout: Optional[str] = Field(None, description="标准输出")
    stderr: Optional[str] = Field(None, description="错误输出")
    return_code: Optional[int] = Field(None, description="返回码")


class TestStatusResponse(TaskStatusResponse):
    """测试任务状态响应模型"""
    filename: Optional[str] = Field(None, description="文件名")
    hw_log: Optional[str] = Field(None, description="网关处理日志")
    hw_err: Optional[str] = Field(None, description="网关错误日志")
    json_events: Optional[list[dict[str, Any]]] = Field(None, description="JSON事件数据")


class TaskSummary(BaseModel):
    """任务摘要模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    message: str = Field(..., description="任务消息")
    filename: Optional[str] = Field(None, description="文件名")
    created_at: datetime = Field(..., description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    class Config:
        use_enum_values = True


class TaskListResponse(BaseModel):
    """任务列表响应模型"""
    total: int = Field(..., description="任务总数")
    tasks: list[TaskSummary] = Field(..., description="任务列表")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[dict[str, Any]] = Field(None, description="错误详情")


class SuccessResponse(BaseModel):
    """成功响应模型"""
    status: str = Field(default="success", description="状态")
    message: str = Field(..., description="成功消息")
    data: Optional[dict[str, Any]] = Field(None, description="响应数据")