"""服务器凭据相关数据模型

定义服务器凭据管理的数据结构。
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, IPvAnyAddress


class ServerCredential(BaseModel):
    """服务器凭据数据模型"""
    server_ip: str = Field(..., description="服务器IP地址", pattern=r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$")
    ssh_user: str = Field(..., description="SSH用户名", min_length=1, max_length=50)
    ssh_password: str = Field(..., description="SSH密码", min_length=1)
    description: Optional[str] = Field("", description="服务器描述", max_length=200)
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CredentialCreateRequest(BaseModel):
    """创建凭据请求模型"""
    server_ip: str = Field(..., description="服务器IP地址", pattern=r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$")
    ssh_user: str = Field(..., description="SSH用户名", min_length=1, max_length=50)
    ssh_password: str = Field(..., description="SSH密码", min_length=1)
    description: Optional[str] = Field("", description="服务器描述", max_length=200)


class CredentialUpdateRequest(BaseModel):
    """更新凭据请求模型"""
    ssh_user: Optional[str] = Field(None, description="SSH用户名", min_length=1, max_length=50)
    ssh_password: Optional[str] = Field(None, description="SSH密码", min_length=1)
    description: Optional[str] = Field(None, description="服务器描述", max_length=200)


class CredentialResponse(BaseModel):
    """凭据响应模型（不包含密码）"""
    server_ip: str = Field(..., description="服务器IP地址")
    ssh_user: str = Field(..., description="SSH用户名")
    description: str = Field(..., description="服务器描述")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CredentialListResponse(BaseModel):
    """凭据列表响应模型"""
    credentials: list[CredentialResponse] = Field(..., description="凭据列表")
    total: int = Field(..., description="总数量")
    message: str = Field("获取凭据列表成功", description="响应消息")


class CredentialBatchImportRequest(BaseModel):
    """批量导入凭据请求模型"""
    credentials: list[CredentialCreateRequest] = Field(..., description="凭据列表", min_items=1)
    overwrite_existing: bool = Field(False, description="是否覆盖已存在的凭据")


class CredentialBatchImportResponse(BaseModel):
    """批量导入凭据响应模型"""
    success_count: int = Field(..., description="成功导入数量")
    failed_count: int = Field(..., description="失败数量")
    failed_items: list[str] = Field(..., description="失败的服务器IP列表")
    message: str = Field(..., description="响应消息")


class CredentialBackupData(BaseModel):
    """凭据备份数据模型"""
    credentials: list[ServerCredential] = Field(..., description="凭据列表")
    backup_time: datetime = Field(default_factory=datetime.now, description="备份时间")
    version: str = Field("1.0", description="备份格式版本")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
