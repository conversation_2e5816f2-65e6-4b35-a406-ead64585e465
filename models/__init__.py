"""数据模型模块

定义应用程序的数据模型和响应模型。
"""

from .task_models import (
    TaskStatus,
    BaseTask,
    BuildTask,
    TestTask
)

from .request_models import (
    BuildRequest,
    DeployRequest,
    BuildPluginRequest,
    DeployPluginRequest,
    BuildDeployRequest,
    BuildDeployPluginRequest,
    PcapTestRequest,
    PcapUploadRequest,
    PcapReplayRequest
)

from .response_models import (
    TaskResponse,
    TaskStatusResponse,
    TaskListResponse,
    BuildStatusResponse,
    TestStatusResponse,
    ErrorResponse,
    SuccessResponse
)

from .credential_models import (
    ServerCredential,
    CredentialCreateRequest,
    CredentialUpdateRequest,
    CredentialResponse,
    CredentialListResponse,
    CredentialBatchImportRequest,
    CredentialBatchImportResponse,
    CredentialBackupData
)

__all__ = [
    # 任务模型
    "TaskStatus",
    "BaseTask",
    "BuildTask",
    "TestTask",

    # 请求模型
    "BuildRequest",
    "DeployRequest",
    "BuildPluginRequest",
    "DeployPluginRequest",
    "BuildDeployRequest",
    "BuildDeployPluginRequest",
    "PcapTestRequest",
    "PcapUploadRequest",
    "PcapReplayRequest",

    # 响应模型
    "TaskResponse",
    "TaskStatusResponse",
    "TaskListResponse",
    "BuildStatusResponse",
    "TestStatusResponse",
    "ErrorResponse",
    "SuccessResponse",

    # 凭据模型
    "ServerCredential",
    "CredentialCreateRequest",
    "CredentialUpdateRequest",
    "CredentialResponse",
    "CredentialListResponse",
    "CredentialBatchImportRequest",
    "CredentialBatchImportResponse",
    "CredentialBackupData"
]