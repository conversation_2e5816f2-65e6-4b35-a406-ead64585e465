"""自定义异常模块

定义应用程序的自定义异常类。
"""

from typing import Optional, Any


class GWHWMCPException(Exception):
    """GWHW MCP基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Any] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details
        super().__init__(self.message)


class TaskNotFoundException(GWHWMCPException):
    """任务未找到异常"""
    
    def __init__(self, task_id: str):
        super().__init__(
            message=f"任务未找到: {task_id}",
            error_code="TASK_NOT_FOUND",
            details={"task_id": task_id}
        )


class SSHConnectionException(GWHWMCPException):
    """SSH连接异常"""
    
    def __init__(self, host: str, error: str):
        super().__init__(
            message=f"SSH连接失败 {host}: {error}",
            error_code="SSH_CONNECTION_ERROR",
            details={"host": host, "error": error}
        )


class RedisConnectionException(GWHWMCPException):
    """Redis连接异常"""
    
    def __init__(self, error: str):
        super().__init__(
            message=f"Redis连接失败: {error}",
            error_code="REDIS_CONNECTION_ERROR",
            details={"error": error}
        )


class TaskExecutionException(GWHWMCPException):
    """任务执行异常"""

    def __init__(self, task_id: str, error: str):
        super().__init__(
            message=f"任务执行失败 {task_id}: {error}",
            error_code="TASK_EXECUTION_ERROR",
            details={"task_id": task_id, "error": error}
        )


class CredentialNotFoundException(GWHWMCPException):
    """凭据未找到异常"""

    def __init__(self, server_ip: str):
        super().__init__(
            message=f"服务器凭据未找到: {server_ip}",
            error_code="CREDENTIAL_NOT_FOUND",
            details={"server_ip": server_ip}
        )


class CredentialAlreadyExistsException(GWHWMCPException):
    """凭据已存在异常"""

    def __init__(self, server_ip: str):
        super().__init__(
            message=f"服务器凭据已存在: {server_ip}",
            error_code="CREDENTIAL_ALREADY_EXISTS",
            details={"server_ip": server_ip}
        )