"""日志配置模块

提供统一的日志配置和管理功能。
"""

import logging
import sys
import os
from typing import Optional
from config.settings import settings


def setup_logging(level: Optional[str] = None) -> None:
    """设置日志配置
    
    Args:
        level: 日志级别，默认根据debug配置决定
    """
    if level is None:
        level = "DEBUG" if settings.debug else "INFO"
    
    # 确保日志目录存在
    log_dir = os.path.dirname(settings.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    err_log_dir = os.path.dirname(settings.log_err)
    if err_log_dir and not os.path.exists(err_log_dir):
        os.makedirs(err_log_dir, exist_ok=True)
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建格式化器 - 使用易读的格式
    # 格式：[2025-05-21 15:04:06] [INFO] [main] 消息内容
    readable_formatter = logging.Formatter(
        '[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 创建控制台处理器（保持原有的控制台输出）
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(readable_formatter)
    root_logger.addHandler(console_handler)
    
    # 创建文件处理器 - 普通日志文件（INFO及以上级别）
    file_handler = logging.FileHandler(settings.log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(readable_formatter)
    root_logger.addHandler(file_handler)
    
    # 创建错误日志文件处理器（ERROR及以上级别）
    error_handler = logging.FileHandler(settings.log_err, encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(readable_formatter)
    root_logger.addHandler(error_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("redis").setLevel(logging.WARNING)
    logging.getLogger("paramiko").setLevel(logging.WARNING)
    logging.getLogger("asyncssh").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    return logging.getLogger(name) 