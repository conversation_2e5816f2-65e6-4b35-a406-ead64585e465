"""测试优雅关闭功能

验证应用程序能够正确处理Ctrl+C信号并优雅关闭，不会出现阻塞问题。
"""

import pytest
import asyncio
import threading
import time
from unittest.mock import AsyncMock, patch, MagicMock
from utils.kafka_client import KafkaClient, cleanup_kafka_resources, cleanup_kafka_resources_async


class TestGracefulShutdown:
    """测试优雅关闭功能"""

    @pytest.mark.asyncio
    async def test_kafka_disconnect_with_timeout(self):
        """测试Kafka断开连接的超时机制"""
        kafka_client = KafkaClient()
        
        # 模拟消费者
        mock_consumer = AsyncMock()
        kafka_client.consumer = mock_consumer
        kafka_client.is_connected = True
        
        # 模拟consumer.stop()超时
        async def slow_stop():
            await asyncio.sleep(10)  # 模拟长时间阻塞
        
        mock_consumer.stop.side_effect = slow_stop
        
        # 测试断开连接应该在超时后完成
        start_time = time.time()
        await kafka_client.disconnect()
        end_time = time.time()
        
        # 验证在超时时间内完成（5秒超时 + 一些缓冲时间）
        assert end_time - start_time < 7.0
        assert kafka_client.consumer is None
        assert not kafka_client.is_connected

    @pytest.mark.asyncio
    async def test_kafka_disconnect_normal(self):
        """测试Kafka正常断开连接"""
        kafka_client = KafkaClient()
        
        # 模拟消费者
        mock_consumer = AsyncMock()
        kafka_client.consumer = mock_consumer
        kafka_client.is_connected = True
        
        # 模拟正常的stop操作
        mock_consumer.stop.return_value = None
        
        # 测试正常断开连接
        await kafka_client.disconnect()
        
        # 验证状态正确重置
        assert kafka_client.consumer is None
        assert not kafka_client.is_connected
        mock_consumer.stop.assert_called_once()

    @pytest.mark.asyncio
    async def test_kafka_disconnect_exception(self):
        """测试Kafka断开连接时的异常处理"""
        kafka_client = KafkaClient()
        
        # 模拟消费者
        mock_consumer = AsyncMock()
        kafka_client.consumer = mock_consumer
        kafka_client.is_connected = True
        
        # 模拟stop操作抛出异常
        mock_consumer.stop.side_effect = Exception("Connection error")
        
        # 测试异常情况下的断开连接
        await kafka_client.disconnect()
        
        # 验证即使出现异常，状态也被正确重置
        assert kafka_client.consumer is None
        assert not kafka_client.is_connected

    @pytest.mark.asyncio
    async def test_cleanup_kafka_resources_async(self):
        """测试异步Kafka资源清理"""
        # 模拟全局kafka_client
        with patch('utils.kafka_client.kafka_client') as mock_kafka_client:
            mock_kafka_client.is_connected = True
            mock_kafka_client.disconnect = AsyncMock()
            
            # 测试异步清理
            await cleanup_kafka_resources_async()
            
            # 验证disconnect被调用
            mock_kafka_client.disconnect.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_kafka_resources_async_timeout(self):
        """测试异步Kafka资源清理超时"""
        # 模拟全局kafka_client
        with patch('utils.kafka_client.kafka_client') as mock_kafka_client:
            mock_kafka_client.is_connected = True
            
            # 模拟disconnect超时
            async def slow_disconnect():
                await asyncio.sleep(10)
            
            mock_kafka_client.disconnect.side_effect = slow_disconnect
            
            # 测试清理应该在超时后完成
            start_time = time.time()
            await cleanup_kafka_resources_async()
            end_time = time.time()
            
            # 验证在超时时间内完成（3秒超时 + 一些缓冲时间）
            assert end_time - start_time < 5.0

    def test_cleanup_kafka_resources_sync(self):
        """测试同步Kafka资源清理（用于atexit）"""
        # 模拟全局kafka_client
        with patch('utils.kafka_client.kafka_client') as mock_kafka_client:
            mock_kafka_client.is_connected = True
            mock_kafka_client.disconnect = AsyncMock()
            
            # 测试同步清理
            start_time = time.time()
            cleanup_kafka_resources()
            end_time = time.time()
            
            # 验证在合理时间内完成（最多5秒线程超时 + 缓冲时间）
            assert end_time - start_time < 7.0

    def test_cleanup_kafka_resources_sync_timeout(self):
        """测试同步Kafka资源清理超时处理"""
        # 模拟全局kafka_client
        with patch('utils.kafka_client.kafka_client') as mock_kafka_client:
            mock_kafka_client.is_connected = True
            
            # 模拟disconnect超时
            async def slow_disconnect():
                await asyncio.sleep(10)
            
            mock_kafka_client.disconnect.side_effect = slow_disconnect
            
            # 测试清理应该在超时后完成
            start_time = time.time()
            cleanup_kafka_resources()
            end_time = time.time()
            
            # 验证在超时时间内完成（5秒线程超时 + 缓冲时间）
            assert end_time - start_time < 7.0
            
            # 验证状态被强制重置
            assert mock_kafka_client.consumer is None
            assert not mock_kafka_client.is_connected

    def test_cleanup_kafka_resources_not_connected(self):
        """测试未连接时的资源清理"""
        # 模拟全局kafka_client未连接
        with patch('utils.kafka_client.kafka_client') as mock_kafka_client:
            mock_kafka_client.is_connected = False
            
            # 测试清理应该快速完成
            start_time = time.time()
            cleanup_kafka_resources()
            end_time = time.time()
            
            # 验证快速完成（应该几乎立即返回）
            assert end_time - start_time < 1.0

    @pytest.mark.asyncio
    async def test_signal_handling_simulation(self):
        """模拟信号处理场景"""
        # 这个测试模拟应用程序接收到SIGINT信号时的处理流程
        
        # 模拟FastAPI lifespan中的清理过程
        with patch('utils.kafka_client.kafka_client') as mock_kafka_client:
            mock_kafka_client.is_connected = True
            mock_kafka_client.disconnect = AsyncMock()
            
            # 模拟lifespan清理
            await cleanup_kafka_resources_async()
            
            # 验证清理被正确执行
            mock_kafka_client.disconnect.assert_called_once()

    def test_thread_safety(self):
        """测试多线程环境下的资源清理"""
        # 模拟多个线程同时调用清理函数
        with patch('utils.kafka_client.kafka_client') as mock_kafka_client:
            mock_kafka_client.is_connected = True
            mock_kafka_client.disconnect = AsyncMock()
            
            # 创建多个线程同时调用清理函数
            threads = []
            for i in range(3):
                thread = threading.Thread(target=cleanup_kafka_resources)
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=10)
            
            # 验证所有线程都正常完成
            for thread in threads:
                assert not thread.is_alive()
