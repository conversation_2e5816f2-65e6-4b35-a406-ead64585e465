"""Kafka客户端测试

测试Kafka消费者功能。
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from aiokafka.errors import KafkaError

from utils.kafka_client import KafkaClient, get_kafka_events
from config.settings import settings


class TestKafkaClient:
    """Kafka客户端测试类"""

    @pytest.fixture
    def kafka_client(self):
        """创建Kafka客户端实例"""
        return KafkaClient()

    @pytest.mark.asyncio
    async def test_connect_success(self, kafka_client):
        """测试Kafka连接成功"""
        with patch('utils.kafka_client.AIOKafkaConsumer') as mock_consumer_class:
            mock_consumer = AsyncMock()
            mock_consumer_class.return_value = mock_consumer
            
            result = await kafka_client.connect()
            
            assert result is True
            assert kafka_client.is_connected is True
            mock_consumer.start.assert_called_once()

    @pytest.mark.asyncio
    async def test_connect_failure(self, kafka_client):
        """测试Kafka连接失败"""
        with patch('utils.kafka_client.AIOKafkaConsumer') as mock_consumer_class:
            mock_consumer = AsyncMock()
            mock_consumer.start.side_effect = KafkaError("连接失败")
            mock_consumer_class.return_value = mock_consumer
            
            result = await kafka_client.connect()
            
            assert result is False
            assert kafka_client.is_connected is False

    @pytest.mark.asyncio
    async def test_disconnect(self, kafka_client):
        """测试Kafka断开连接"""
        mock_consumer = AsyncMock()
        kafka_client.consumer = mock_consumer
        kafka_client.is_connected = True
        
        await kafka_client.disconnect()
        
        assert kafka_client.is_connected is False
        mock_consumer.stop.assert_called_once()

    @pytest.mark.asyncio
    async def test_consume_events_success(self, kafka_client):
        """测试消费事件成功"""
        # 模拟消费者和消息
        mock_consumer = AsyncMock()
        kafka_client.consumer = mock_consumer
        kafka_client.is_connected = True
        
        # 模拟消息数据
        mock_message = MagicMock()
        mock_message.value = '{"event_type": "test", "data": "sample"}'
        
        mock_consumer.getmany.return_value = {
            "topic_partition": [mock_message]
        }
        
        events = await kafka_client.consume_events(timeout_seconds=1, max_records=10)
        
        assert len(events) == 1
        assert events[0]["event_type"] == "test"
        assert events[0]["data"] == "sample"

    @pytest.mark.asyncio
    async def test_consume_events_json_error(self, kafka_client):
        """测试消费事件JSON解析错误"""
        mock_consumer = AsyncMock()
        kafka_client.consumer = mock_consumer
        kafka_client.is_connected = True
        
        # 模拟无效JSON消息
        mock_message = MagicMock()
        mock_message.value = 'invalid json'
        
        mock_consumer.getmany.return_value = {
            "topic_partition": [mock_message]
        }
        
        events = await kafka_client.consume_events(timeout_seconds=1, max_records=10)
        
        assert len(events) == 1
        assert "error" in events[0]
        assert "JSON解析失败" in events[0]["error"]
        assert events[0]["raw_content"] == "invalid json"

    @pytest.mark.asyncio
    async def test_consume_events_not_connected(self, kafka_client):
        """测试未连接时消费事件"""
        kafka_client.is_connected = False
        kafka_client.consumer = None
        
        events = await kafka_client.consume_events()
        
        assert events == []

    @pytest.mark.asyncio
    async def test_get_recent_events_auto_connect(self, kafka_client):
        """测试获取最近事件时自动连接"""
        kafka_client.is_connected = False
        
        with patch.object(kafka_client, 'connect', return_value=True) as mock_connect, \
             patch.object(kafka_client, 'consume_events', return_value=[{"test": "data"}]) as mock_consume:
            
            events = await kafka_client.get_recent_events()
            
            mock_connect.assert_called_once()
            mock_consume.assert_called_once()
            assert len(events) == 1

    @pytest.mark.asyncio
    async def test_get_recent_events_connect_failure(self, kafka_client):
        """测试获取最近事件时连接失败"""
        kafka_client.is_connected = False
        
        with patch.object(kafka_client, 'connect', return_value=False) as mock_connect:
            
            events = await kafka_client.get_recent_events()
            
            mock_connect.assert_called_once()
            assert events == []

    @pytest.mark.asyncio
    async def test_context_manager(self, kafka_client):
        """测试异步上下文管理器"""
        with patch.object(kafka_client, 'connect', return_value=True) as mock_connect, \
             patch.object(kafka_client, 'disconnect') as mock_disconnect:
            
            async with kafka_client as client:
                assert client is kafka_client
                mock_connect.assert_called_once()
            
            mock_disconnect.assert_called_once()


class TestKafkaUtilityFunctions:
    """Kafka工具函数测试类"""

    @pytest.mark.asyncio
    async def test_get_kafka_events(self):
        """测试获取Kafka事件工具函数"""
        with patch('utils.kafka_client.kafka_client') as mock_client:
            mock_client.get_recent_events.return_value = [{"test": "event"}]
            
            events = await get_kafka_events(timeout_seconds=5)
            
            mock_client.get_recent_events.assert_called_once_with(5)
            assert len(events) == 1
            assert events[0]["test"] == "event"


class TestKafkaConfiguration:
    """Kafka配置测试类"""

    def test_kafka_settings(self):
        """测试Kafka配置项"""
        assert hasattr(settings, 'kafka_bootstrap_servers')
        assert hasattr(settings, 'kafka_topic_db_event')
        assert hasattr(settings, 'kafka_consumer_group')
        assert hasattr(settings, 'kafka_consumer_timeout')
        assert hasattr(settings, 'kafka_max_poll_records')
        
        assert settings.kafka_bootstrap_servers == "192.168.21.249:9092"
        assert settings.kafka_topic_db_event == "DBEvent"
        assert settings.kafka_consumer_group == "gwhw_mcp_consumer"
        assert settings.kafka_consumer_timeout == 10
        assert settings.kafka_max_poll_records == 100
