"""凭据服务测试

测试凭据管理服务的各项功能。
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime

from services.credential_service import CredentialService
from models.credential_models import (
    CredentialCreateRequest,
    CredentialUpdateRequest,
    CredentialBatchImportRequest
)
from core.exceptions import CredentialNotFoundException, CredentialAlreadyExistsException
from utils.crypto_utils import CryptoUtils


class TestCredentialService:
    """凭据服务测试类"""
    
    @pytest.fixture
    def mock_redis_client(self):
        """模拟Redis客户端"""
        mock_client = AsyncMock()
        mock_client.set_task = AsyncMock(return_value=True)
        mock_client.get_task = AsyncMock(return_value=None)
        mock_client.exists = AsyncMock(return_value=False)
        mock_client.delete = AsyncMock(return_value=True)
        mock_client.scan_keys = AsyncMock(return_value=[])
        return mock_client
    
    @pytest.fixture
    def credential_service(self, mock_redis_client):
        """创建凭据服务实例"""
        service = CredentialService(mock_redis_client)
        # 使用测试密钥
        service.encryption_key = CryptoUtils.generate_fernet_key()
        return service
    
    @pytest.mark.asyncio
    async def test_create_credential_success(self, credential_service, mock_redis_client):
        """测试成功创建凭据"""
        # 准备测试数据
        request = CredentialCreateRequest(
            server_ip="*************",
            ssh_user="testuser",
            ssh_password="testpass",
            description="测试服务器"
        )
        
        # 执行测试
        response = await credential_service.create_credential(request)
        
        # 验证结果
        assert response.server_ip == "*************"
        assert response.ssh_user == "testuser"
        assert response.description == "测试服务器"
        assert mock_redis_client.set_task.called
    
    @pytest.mark.asyncio
    async def test_create_credential_already_exists(self, credential_service, mock_redis_client):
        """测试创建已存在的凭据"""
        # 模拟凭据已存在
        mock_redis_client.exists.return_value = True
        
        request = CredentialCreateRequest(
            server_ip="*************",
            ssh_user="testuser",
            ssh_password="testpass"
        )
        
        # 执行测试并验证异常
        with pytest.raises(CredentialAlreadyExistsException):
            await credential_service.create_credential(request)
    
    @pytest.mark.asyncio
    async def test_get_credential_success(self, credential_service, mock_redis_client):
        """测试成功获取凭据"""
        # 准备模拟数据
        encrypted_password = CryptoUtils.encrypt_password("testpass", credential_service.encryption_key)
        mock_data = {
            "server_ip": "*************",
            "ssh_user": "testuser",
            "ssh_password": encrypted_password,
            "description": "测试服务器",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        mock_redis_client.get_task.return_value = mock_data
        
        # 执行测试
        credential = await credential_service.get_credential("*************", include_password=True)
        
        # 验证结果
        assert credential is not None
        assert credential.server_ip == "*************"
        assert credential.ssh_user == "testuser"
        assert credential.ssh_password == "testpass"  # 应该被解密
    
    @pytest.mark.asyncio
    async def test_get_credential_not_found(self, credential_service, mock_redis_client):
        """测试获取不存在的凭据"""
        # 模拟凭据不存在
        mock_redis_client.get_task.return_value = None
        
        # 执行测试
        credential = await credential_service.get_credential("*************")
        
        # 验证结果
        assert credential is None
    
    @pytest.mark.asyncio
    async def test_update_credential_success(self, credential_service, mock_redis_client):
        """测试成功更新凭据"""
        # 准备现有凭据数据
        encrypted_password = CryptoUtils.encrypt_password("oldpass", credential_service.encryption_key)
        existing_data = {
            "server_ip": "*************",
            "ssh_user": "olduser",
            "ssh_password": encrypted_password,
            "description": "旧描述",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        mock_redis_client.get_task.return_value = existing_data
        
        # 准备更新请求
        update_request = CredentialUpdateRequest(
            ssh_user="newuser",
            ssh_password="newpass",
            description="新描述"
        )
        
        # 执行测试
        response = await credential_service.update_credential("*************", update_request)
        
        # 验证结果
        assert response.ssh_user == "newuser"
        assert response.description == "新描述"
        assert mock_redis_client.set_task.called
    
    @pytest.mark.asyncio
    async def test_update_credential_not_found(self, credential_service, mock_redis_client):
        """测试更新不存在的凭据"""
        # 模拟凭据不存在
        mock_redis_client.get_task.return_value = None
        
        update_request = CredentialUpdateRequest(ssh_user="newuser")
        
        # 执行测试并验证异常
        with pytest.raises(CredentialNotFoundException):
            await credential_service.update_credential("*************", update_request)
    
    @pytest.mark.asyncio
    async def test_delete_credential_success(self, credential_service, mock_redis_client):
        """测试成功删除凭据"""
        # 模拟凭据存在
        mock_redis_client.exists.return_value = True
        mock_redis_client.delete.return_value = True
        
        # 执行测试
        result = await credential_service.delete_credential("*************")
        
        # 验证结果
        assert result is True
        assert mock_redis_client.delete.called
    
    @pytest.mark.asyncio
    async def test_delete_credential_not_found(self, credential_service, mock_redis_client):
        """测试删除不存在的凭据"""
        # 模拟凭据不存在
        mock_redis_client.exists.return_value = False
        
        # 执行测试并验证异常
        with pytest.raises(CredentialNotFoundException):
            await credential_service.delete_credential("*************")
    
    @pytest.mark.asyncio
    async def test_list_credentials(self, credential_service, mock_redis_client):
        """测试获取凭据列表"""
        # 准备模拟数据
        encrypted_password = CryptoUtils.encrypt_password("testpass", credential_service.encryption_key)
        mock_data = {
            "server_ip": "*************",
            "ssh_user": "testuser",
            "ssh_password": encrypted_password,
            "description": "测试服务器",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        mock_redis_client.scan_keys.return_value = ["server_credential:*************"]
        mock_redis_client.get_task.return_value = mock_data
        
        # 执行测试
        response = await credential_service.list_credentials()
        
        # 验证结果
        assert response.total == 1
        assert len(response.credentials) == 1
        assert response.credentials[0].server_ip == "*************"
    
    @pytest.mark.asyncio
    async def test_batch_import_credentials(self, credential_service, mock_redis_client):
        """测试批量导入凭据"""
        # 准备测试数据
        credentials = [
            CredentialCreateRequest(
                server_ip="*************",
                ssh_user="user1",
                ssh_password="pass1"
            ),
            CredentialCreateRequest(
                server_ip="*************",
                ssh_user="user2",
                ssh_password="pass2"
            )
        ]
        
        batch_request = CredentialBatchImportRequest(
            credentials=credentials,
            overwrite_existing=False
        )
        
        # 执行测试
        response = await credential_service.batch_import_credentials(batch_request)
        
        # 验证结果
        assert response.success_count == 2
        assert response.failed_count == 0
    
    @pytest.mark.asyncio
    async def test_get_credential_for_ssh(self, credential_service, mock_redis_client):
        """测试获取SSH凭据"""
        # 准备模拟数据
        encrypted_password = CryptoUtils.encrypt_password("testpass", credential_service.encryption_key)
        mock_data = {
            "server_ip": "*************",
            "ssh_user": "testuser",
            "ssh_password": encrypted_password,
            "description": "测试服务器",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        mock_redis_client.get_task.return_value = mock_data
        
        # 执行测试
        ssh_info = await credential_service.get_credential_for_ssh("*************")
        
        # 验证结果
        assert ssh_info is not None
        assert ssh_info["ssh_user"] == "testuser"
        assert ssh_info["ssh_password"] == "testpass"
