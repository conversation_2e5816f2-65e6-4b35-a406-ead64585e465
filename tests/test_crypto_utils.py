"""加密工具测试

测试加密工具的各项功能。
"""

import pytest
from utils.crypto_utils import CryptoUtils


class TestCryptoUtils:
    """加密工具测试类"""
    
    def test_generate_encryption_key(self):
        """测试生成加密密钥"""
        key = CryptoUtils.generate_encryption_key()
        
        # 验证密钥格式
        assert isinstance(key, str)
        assert len(key) > 0
        
        # 验证密钥可以用于创建加密器
        assert CryptoUtils.validate_key(key)
    
    def test_generate_fernet_key(self):
        """测试生成Fernet密钥"""
        key = CryptoUtils.generate_fernet_key()
        
        # 验证密钥格式
        assert isinstance(key, str)
        assert len(key) > 0
        
        # 验证密钥可以用于创建加密器
        assert CryptoUtils.validate_key(key)
    
    def test_encrypt_decrypt_password(self):
        """测试密码加密和解密"""
        # 生成测试密钥
        key = CryptoUtils.generate_fernet_key()
        original_password = "test_password_123"
        
        # 加密密码
        encrypted = CryptoUtils.encrypt_password(original_password, key)
        
        # 验证加密结果
        assert isinstance(encrypted, str)
        assert encrypted != original_password
        assert len(encrypted) > 0
        
        # 解密密码
        decrypted = CryptoUtils.decrypt_password(encrypted, key)
        
        # 验证解密结果
        assert decrypted == original_password
    
    def test_encrypt_decrypt_with_different_keys(self):
        """测试使用不同密钥加密解密"""
        key1 = CryptoUtils.generate_fernet_key()
        key2 = CryptoUtils.generate_fernet_key()
        password = "test_password"
        
        # 使用key1加密
        encrypted = CryptoUtils.encrypt_password(password, key1)
        
        # 使用key2解密应该失败
        with pytest.raises(Exception):
            CryptoUtils.decrypt_password(encrypted, key2)
    
    def test_validate_key_valid(self):
        """测试验证有效密钥"""
        key = CryptoUtils.generate_fernet_key()
        assert CryptoUtils.validate_key(key) is True
    
    def test_validate_key_invalid(self):
        """测试验证无效密钥"""
        invalid_keys = [
            "invalid_key",
            "",
            "too_short",
            "this_is_not_a_valid_fernet_key_at_all"
        ]
        
        for invalid_key in invalid_keys:
            assert CryptoUtils.validate_key(invalid_key) is False
    
    def test_create_cipher_with_32_byte_key(self):
        """测试使用32字节密钥创建加密器"""
        # 生成32字节密钥
        key = CryptoUtils.generate_encryption_key()
        
        # 创建加密器
        cipher = CryptoUtils.create_cipher(key)
        
        # 验证加密器可以正常工作
        test_data = "test_data"
        encrypted = cipher.encrypt(test_data.encode())
        decrypted = cipher.decrypt(encrypted).decode()
        assert decrypted == test_data
    
    def test_create_cipher_with_fernet_key(self):
        """测试使用Fernet密钥创建加密器"""
        # 生成Fernet密钥
        key = CryptoUtils.generate_fernet_key()
        
        # 创建加密器
        cipher = CryptoUtils.create_cipher(key)
        
        # 验证加密器可以正常工作
        test_data = "test_data"
        encrypted = cipher.encrypt(test_data.encode())
        decrypted = cipher.decrypt(encrypted).decode()
        assert decrypted == test_data
    
    def test_generate_secure_config_template(self):
        """测试生成安全配置模板"""
        template = CryptoUtils.generate_secure_config_template()
        
        # 验证模板内容
        assert isinstance(template, str)
        assert "CREDENTIAL_ENCRYPTION_KEY=" in template
        assert "CREDENTIAL_REDIS_PREFIX=" in template
        assert "ENABLE_CREDENTIAL_AUDIT=" in template
        
        # 提取密钥并验证
        lines = template.split('\n')
        key_line = next(line for line in lines if line.startswith('CREDENTIAL_ENCRYPTION_KEY='))
        key = key_line.split('=', 1)[1]
        assert CryptoUtils.validate_key(key)
    
    def test_encrypt_decrypt_empty_password(self):
        """测试加密解密空密码"""
        key = CryptoUtils.generate_fernet_key()
        empty_password = ""
        
        # 加密空密码
        encrypted = CryptoUtils.encrypt_password(empty_password, key)
        
        # 解密并验证
        decrypted = CryptoUtils.decrypt_password(encrypted, key)
        assert decrypted == empty_password
    
    def test_encrypt_decrypt_unicode_password(self):
        """测试加密解密Unicode密码"""
        key = CryptoUtils.generate_fernet_key()
        unicode_password = "测试密码🔐"
        
        # 加密Unicode密码
        encrypted = CryptoUtils.encrypt_password(unicode_password, key)
        
        # 解密并验证
        decrypted = CryptoUtils.decrypt_password(encrypted, key)
        assert decrypted == unicode_password
    
    def test_encrypt_decrypt_long_password(self):
        """测试加密解密长密码"""
        key = CryptoUtils.generate_fernet_key()
        long_password = "a" * 1000  # 1000字符的密码
        
        # 加密长密码
        encrypted = CryptoUtils.encrypt_password(long_password, key)
        
        # 解密并验证
        decrypted = CryptoUtils.decrypt_password(encrypted, key)
        assert decrypted == long_password
