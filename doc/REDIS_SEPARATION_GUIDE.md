# Redis数据库分离实施指南

## 📋 概述

本指南详细说明了如何将GWHW网关MCP服务的Redis数据库进行分离，将凭据管理数据从0号数据库迁移到1号数据库，实现不同类型数据的隔离。

## 🎯 分离目标

### 数据库分配
- **DB0 (任务数据库)**: 构建任务、测试任务、任务集合
- **DB1 (凭据数据库)**: 服务器凭据、SSH认证信息

### 分离优势
1. **安全隔离**: 敏感凭据数据与任务数据分离
2. **独立管理**: 不同数据类型可独立备份和恢复
3. **权限控制**: 可为不同数据库设置不同的访问权限
4. **性能优化**: 减少数据冲突，提高查询效率

## 🔧 实施步骤

### 步骤1: 停止服务
```bash
# 停止GWHW网关MCP服务
pkill -f "python main.py"
```

### 步骤2: 备份现有数据
```bash
# 备份Redis数据
redis-cli -h 192.168.21.249 -p 6379 -a qzkj --rdb backup_before_separation.rdb
```

### 步骤3: 检查当前数据分布
```bash
# 激活虚拟环境
source .venv/bin/activate

# 运行迁移工具检查状态
python migrate_credentials_to_db1.py
# 选择选项1: 检查迁移状态
```

### 步骤4: 执行数据迁移
```bash
# 运行迁移工具
python migrate_credentials_to_db1.py
# 选择选项2: 执行迁移
# 按提示完成迁移过程
```

### 步骤5: 验证分离效果
```bash
# 运行验证脚本
python verify_redis_separation.py
# 选择选项2: 执行分离验证测试
```

### 步骤6: 启动服务
```bash
# 启动服务
python main.py
```

### 步骤7: 功能测试
```bash
# 测试凭据管理功能
curl -X GET "http://localhost:8000/api/v1/credentials"

# 测试任务功能
curl -X GET "http://localhost:8000/api/v1/tasks"
```

## 📁 修改的文件清单

### 配置文件
- `config/settings.py`: 添加 `credential_redis_db = 1`
- `config/redis_config.py`: 新增 `CredentialRedisConfig` 类

### 工具文件
- `utils/redis_client.py`: 新增 `CredentialRedisClient` 类
- `utils/__init__.py`: 更新导出列表

### 服务文件
- `services/credential_service.py`: 使用 `CredentialRedisClient`
- `main.py`: 初始化两个Redis客户端

### 新增文件
- `migrate_credentials_to_db1.py`: 数据迁移脚本
- `verify_redis_separation.py`: 验证脚本

## ⚠️ 注意事项

### 迁移前检查
1. 确保Redis服务正常运行
2. 确认有足够的磁盘空间
3. 备份现有数据

### 迁移过程
1. 迁移过程中服务应该停止
2. 迁移完成后验证数据完整性
3. 确认所有凭据都已正确迁移

### 回滚方案
如果迁移出现问题，可以：
1. 停止服务
2. 恢复备份数据
3. 回退代码更改
4. 重新启动服务

## 🔍 验证清单

### 数据隔离验证
- [ ] DB0中只有任务相关数据
- [ ] DB1中只有凭据相关数据
- [ ] 没有数据混合或重复

### 功能验证
- [ ] 凭据管理API正常工作
- [ ] 任务管理API正常工作
- [ ] SSH服务能正确获取凭据
- [ ] 构建部署任务正常执行

### 性能验证
- [ ] 服务启动时间正常
- [ ] API响应时间正常
- [ ] Redis连接稳定

## 📊 监控建议

### 日志监控
关注以下日志信息：
- Redis连接成功日志
- 数据库编号信息
- 凭据操作审计日志

### 性能监控
- Redis连接数
- 查询响应时间
- 内存使用情况

## 🚨 故障排除

### 常见问题

**问题1**: 服务启动失败
```
解决方案:
1. 检查Redis连接配置
2. 确认两个数据库都可访问
3. 查看详细错误日志
```

**问题2**: 凭据获取失败
```
解决方案:
1. 检查凭据是否在DB1中
2. 验证CredentialRedisClient配置
3. 确认凭据服务初始化正确
```

**问题3**: 数据混合
```
解决方案:
1. 运行验证脚本检查
2. 手动清理错误位置的数据
3. 重新执行迁移
```

## 📈 后续优化建议

1. **监控告警**: 设置Redis数据库监控告警
2. **自动备份**: 为不同数据库设置独立的备份策略
3. **权限管理**: 考虑为不同数据库设置不同的访问权限
4. **性能调优**: 根据使用情况调整连接池大小

## 📞 支持联系

如果在实施过程中遇到问题，请：
1. 查看详细日志信息
2. 运行验证脚本诊断
3. 参考故障排除指南
4. 保留现场信息以便分析

---

**重要提醒**: 在生产环境实施前，请在测试环境完整验证整个流程！
