# Kafka 实时消费策略实现文档

## 概述

本文档描述了 GWHW 网关 MCP 服务中新实现的 Kafka 实时消费策略，解决了 pcap 回放测试中历史数据干扰的问题，实现了真正的实时数据收集。

## 问题背景

### 原有问题
1. **连接生命周期绑定**：Kafka 连接与整个应用程序生命周期绑定
2. **历史数据干扰**：每次测试都从头开始消费所有历史数据（相当于 `--from-beginning`）
3. **数据混淆**：测试结果包含历史数据，无法区分当前测试产生的数据
4. **资源浪费**：重复消费大量历史数据，影响性能

### 目标需求
1. **按需连接**：每次执行 pcap 回放测试时，创建新的 Kafka 消费者连接
2. **实时消费**：只消费测试期间产生的新数据，不包含历史数据
3. **连接隔离**：每次测试结束后断开 Kafka 连接，确保下次测试重新连接
4. **数据隔离**：保证每次测试只返回当前测试周期内的 Kafka 事件数据

## 技术实现

### 1. Kafka 客户端改进

#### 新增实时消费函数
```python
async def get_kafka_events_realtime(timeout_seconds: int = 10) -> List[Dict[str, Any]]:
    """获取实时Kafka事件的便捷函数（创建新的消费者连接）
    
    每次调用都会创建新的Kafka消费者连接，只消费当前时间点之后的新数据。
    适用于需要隔离数据的测试场景。
    """
    # 创建新的Kafka客户端实例，确保数据隔离
    async with KafkaClient() as client:
        return await client.consume_events(
            timeout_seconds=timeout_seconds,
            max_records=settings.kafka_max_poll_records
        )
```

#### 关键配置参数
- `auto_offset_reset='latest'`：从最新消息开始消费
- `enable_auto_commit=True`：自动提交偏移量
- 使用异步上下文管理器确保连接自动关闭

### 2. 测试服务集成

#### 新增专用方法
```python
async def _execute_replay_with_realtime_kafka(self, task_id: str, task, pcap_file_path: str) -> dict:
    """执行pcap回放并实时收集Kafka数据"""
    
async def _collect_kafka_events_during_replay(self, kafka_client: KafkaClient, task_id: str, total_timeout: int) -> list:
    """在回放期间持续收集Kafka事件"""
```

#### 执行流程
1. **建立连接**：创建专用的 Kafka 客户端
2. **启动收集**：在后台启动 Kafka 数据收集任务
3. **执行回放**：运行 tcpreplay 命令
4. **等待处理**：等待系统处理数据包
5. **停止收集**：停止 Kafka 数据收集并获取结果
6. **自动清理**：连接自动断开

### 3. SSH 服务扩展

#### 新增方法
```python
async def get_test_logs_without_kafka(self, host: str, user: Optional[str], log_lines: int, protocol_name: Optional[str] = None) -> dict[str, Any]:
    """获取测试日志（不包含Kafka数据）
    
    专门用于实时Kafka数据收集场景，只获取hw.log和hw.err，不获取Kafka事件。
    """
```

#### 职责分离
- `get_test_logs()`：传统方式，包含 Kafka 数据获取
- `get_test_logs_without_kafka()`：新方式，不包含 Kafka 数据获取
- 实时 Kafka 数据由专门的收集任务处理

## 架构对比

### 原有架构
```
应用启动 → 全局Kafka连接 → 多次测试复用连接 → 应用关闭时断开
                ↓
        每次测试获取历史+新数据
```

### 新架构
```
测试开始 → 创建新Kafka连接 → 只获取新数据 → 测试结束断开连接
    ↓              ↓                ↓              ↓
  数据隔离      实时消费          准确结果        资源清理
```

## 时序关系

### 数据收集时序
```
时间轴: ----[tcpreplay开始]----[数据包处理]----[等待时间]----[收集结束]----
Kafka:      ↑开始收集                                    ↑停止收集
数据:       |<------------- 只收集这个时间段的数据 ----------->|
```

### 关键时间点
1. **T0**：创建 Kafka 连接（`auto_offset_reset='latest'`）
2. **T1**：开始 tcpreplay 回放
3. **T2**：tcpreplay 完成
4. **T3**：等待数据处理（`wait_seconds`）
5. **T4**：停止 Kafka 收集，断开连接

## 配置参数

### Kafka 相关配置
```bash
# Kafka服务器配置
KAFKA_BOOTSTRAP_SERVERS=192.168.21.249:9092
KAFKA_TOPIC_DB_EVENT=DBEvent
KAFKA_CONSUMER_GROUP=gwhw_mcp_consumer
KAFKA_CONSUMER_TIMEOUT=10
KAFKA_MAX_POLL_RECORDS=100

# 测试配置
DEFAULT_WAIT_SECONDS=5  # 等待数据处理时间
```

### 消费者配置
- `auto_offset_reset='latest'`：从最新位置开始消费
- `group_id`：使用配置的消费者组
- `enable_auto_commit=True`：自动提交偏移量

## 错误处理和降级

### 错误处理策略
1. **Kafka 连接失败**：记录错误，返回空事件列表
2. **消费超时**：正常结束，返回已收集的事件
3. **网络异常**：自动重试，记录警告日志
4. **数据解析错误**：跳过错误数据，继续处理

### 降级机制
```python
try:
    # 尝试实时Kafka收集
    kafka_events = await collect_realtime_kafka_events()
except Exception as e:
    logger.error(f"实时Kafka收集失败: {e}")
    # 降级到传统方式
    kafka_events = await get_kafka_events_traditional()
```

## 性能优化

### 资源管理
- **连接池**：每次测试使用独立连接，避免连接冲突
- **内存管理**：及时释放 Kafka 客户端资源
- **超时控制**：合理设置各阶段超时时间

### 数据收集优化
- **批量收集**：每次收集2秒的数据，减少网络开销
- **事件限制**：限制单次收集的最大事件数量
- **休眠策略**：适当休眠避免过度消费资源

## 测试验证

### 单元测试
```python
@pytest.mark.asyncio
async def test_realtime_kafka_integration():
    """测试实时Kafka数据收集功能"""
    # 验证Kafka客户端被正确创建和使用
    # 验证数据隔离效果
    # 验证连接自动清理
```

### 集成测试
- 验证与现有功能的兼容性
- 验证错误处理和降级机制
- 验证性能和资源使用

## 使用示例

### API 调用
```bash
curl -X POST "http://localhost:8000/api/v1/test/pcap-replay" \
  -H "Content-Type: application/json" \
  -d '{
    "pcap_file_name": "pgsql_test.pcap",
    "protocol_name": "postgre",
    "remote_ip": "*************",
    "ssh_user": "root",
    "eth_name": "lo"
  }'
```

### 响应示例
```json
{
  "task_id": "uuid",
  "status": "success",
  "result": {
    "logs": {
      "hw_log": "...",
      "hw_err": "...",
      "json_events": [
        {"event_type": "db_insert", "timestamp": "2024-01-01T10:00:00Z"},
        {"event_type": "db_update", "timestamp": "2024-01-01T10:00:01Z"}
      ]
    },
    "tcpreplay_output": {...},
    "kafka_events_count": 2,
    "realtime_data_collection": true
  }
}
```

## 监控和日志

### 关键日志
```
[INFO] 任务 xxx - 已建立实时Kafka连接
[INFO] 任务 xxx - 开始执行tcpreplay命令
[INFO] 任务 xxx - tcpreplay执行成功，等待数据处理
[INFO] 任务 xxx - 实时数据收集完成，共收集 X 个事件
```

### 监控指标
- Kafka 连接成功率
- 数据收集延迟
- 事件收集数量
- 连接清理成功率

## 总结

新的 Kafka 实时消费策略成功解决了历史数据干扰问题，实现了：

1. **✅ 按需连接**：每次测试创建新连接
2. **✅ 实时消费**：只获取测试期间的新数据
3. **✅ 数据隔离**：每次测试数据完全独立
4. **✅ 资源管理**：自动清理连接和资源
5. **✅ 向后兼容**：不影响现有功能

这一改进显著提高了 pcap 回放测试的准确性和可靠性，为用户提供了更精确的测试结果。
