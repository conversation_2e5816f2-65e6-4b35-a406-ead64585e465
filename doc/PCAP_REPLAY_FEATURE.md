# PCAP回放测试功能实现文档

## 功能概述

本文档描述了新实现的 PCAP 回放测试功能，该功能允许对远程服务器上已存在的 pcap 文件进行 tcpreplay 回放测试。

## 接口规格

### API 端点
- **路径**: `/api/v1/test/pcap-replay`
- **方法**: POST
- **内容类型**: application/json

### 请求参数

| 参数名 | 类型 | 必需 | 默认值 | 描述 |
|--------|------|------|--------|------|
| `pcap_file_name` | string | 是 | - | pcap文件名（不包含路径），例如 "pgsql_test.pcap" |
| `protocol_name` | string | 是 | - | 测试协议名称，用于日志过滤和路径构建 |
| `remote_ip` | string | 否 | 配置默认值 | 远程服务器IP |
| `ssh_user` | string | 否 | 配置默认值 | SSH用户名 |
| `eth_name` | string | 否 | "lo" | 网络接口名称 |

### 请求示例

```json
{
  "pcap_file_name": "pgsql_test.pcap",
  "protocol_name": "postgre",
  "remote_ip": "*************",
  "ssh_user": "root",
  "eth_name": "lo"
}
```

### 响应格式

```json
{
  "task_id": "uuid",
  "status": "pending",
  "message": "pcap回放测试任务已创建，正在后台执行 (pgsql_test.pcap)",
  "created_at": "2024-01-01T00:00:00Z"
}
```

## 实现逻辑

### 1. 文件路径构建
- 使用配置项 `pcap_test_dir` + `protocol_name` + `pcap_file_name` 构建完整路径
- 示例：`/opt/pcap/postgre/pgsql_test.pcap`

### 2. 执行流程
1. **文件存在性检查**: 使用 `test -f` 命令检查文件是否存在
2. **tcpreplay 执行**: 使用 `tcpreplay -i {网口名} -M 100 {文件路径}` 命令回放
3. **等待处理**: 等待指定时间让系统处理数据包
4. **日志收集**: 收集相关日志信息
5. **结果返回**: 返回测试结果和 tcpreplay 输出

### 3. 错误处理
- 文件不存在时任务失败
- tcpreplay 执行失败时任务失败
- SSH 连接失败时任务出错

## 代码架构

### 新增文件和修改

#### 1. 数据模型 (`models/request_models.py`)
```python
class PcapReplayRequest(BaseModel):
    """PCAP回放测试请求模型"""
    pcap_file_name: str = Field(..., description="pcap文件名（不包含路径）")
    protocol_name: str = Field(..., description="测试协议名称，用于日志过滤")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    eth_name: Optional[str] = Field("lo", description="网络接口名称")
```

#### 2. 任务模型 (`models/task_models.py`)
- 新增 `pcap_file_name` 字段到 `TestTask` 模型

#### 3. 服务层 (`services/test_service.py`)
- 新增 `start_pcap_replay_test()` 方法
- 新增 `_execute_pcap_replay_existing_test()` 方法

#### 4. API层 (`api/routes.py`)
- 新增 `/api/v1/test/pcap-replay` 端点

#### 5. 任务管理 (`utils/task_manager.py`)
- 更新 `create_test_task()` 方法支持 `pcap_file_name` 参数

### 配置项

#### 新增配置 (`config/settings.py`)
```python
pcap_test_dir: str = "/opt/pcap/"  # pcap测试目录
```

#### 环境变量 (`config.example`)
```bash
PCAP_TEST_DIR=/opt/pcap/  # pcap测试目录（用于回放测试）
```

## 测试

### 单元测试 (`tests/test_pcap_replay_service.py`)
- 测试基本功能
- 测试默认值处理
- 测试文件路径构建逻辑

### 演示脚本 (`demo_pcap_replay.py`)
- 基本用法演示
- 不同协议测试
- 错误处理演示

## 与现有功能的区别

| 功能 | 接口路径 | 文件来源 | 目标目录 | 用途 |
|------|----------|----------|----------|------|
| pcap测试 | `/api/v1/test/pcap` | 服务器已存在 | 不涉及 | 基础测试 |
| pcap上传测试 | `/api/v1/test/pcap-upload` | 客户端上传 | `/opt/pcap/task` | 上传后测试 |
| pcap上传回放测试 | `/api/v1/test/pcap-upload-replay` | 客户端上传 | `/tmp` | 上传后回放 |
| **pcap回放测试** | `/api/v1/test/pcap-replay` | 服务器已存在 | `/opt/pcap/{protocol}/` | **直接回放** |

## 优势

1. **高效性**: 无需文件上传，直接使用服务器上的文件
2. **组织性**: 按协议名称组织文件结构
3. **一致性**: 遵循项目的分层架构和错误处理模式
4. **可扩展性**: 支持多种协议和配置选项
5. **安全性**: 自动检查文件存在性，防止路径注入

## 使用场景

1. **回归测试**: 使用预先准备的标准测试文件
2. **性能测试**: 重复执行相同的测试场景
3. **协议验证**: 针对不同协议进行专门测试
4. **自动化测试**: 集成到CI/CD流程中

## 注意事项

1. **文件组织**: 需要按照 `/opt/pcap/{protocol_name}/` 结构组织测试文件
2. **权限要求**: SSH用户需要有读取测试文件的权限
3. **网络接口**: 确保指定的网络接口存在且可用
4. **协议名称**: 协议名称用于路径构建和日志过滤，需要准确指定

## 配置示例

```bash
# 环境变量配置
PCAP_TEST_DIR=/opt/pcap/
DEFAULT_ETH_NAME=lo
DEFAULT_WAIT_SECONDS=5
DEFAULT_LOG_LINES=300

# 文件组织结构
/opt/pcap/
├── postgre/
│   ├── pgsql_test.pcap
│   └── pgsql_complex.pcap
├── mysql/
│   ├── mysql_test.pcap
│   └── mysql_performance.pcap
└── redis/
    ├── redis_test.pcap
    └── redis_cluster.pcap
```

## 总结

新的 PCAP 回放测试功能为项目提供了一个高效、灵活的测试方案，特别适合需要重复执行标准化测试的场景。该功能完全遵循项目的架构设计原则，与现有功能形成良好的互补关系。
